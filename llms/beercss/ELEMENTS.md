# ELEMENTS

## [Badge](BADGE.md)

Badges are generally used to emphasize some elements and they are placed in element corners.

## [<PERSON><PERSON>](BUTTON.md)

Buttons allow users to take actions, and make choices, with a single tap.

## [Card](CARD.md)

Cards are surfaces that display content and actions on a single topic. They should be easy to scan for relevant and actionable information. Elements, like text and images, should be placed on them in a way that clearly indicates hierarchy.

## [Checkbox](CHECKBOX.md)

Checkboxes allow users to select one or more items from a set. Checkboxes can turn an option on or off.

## [Chip](CHIP.md)

Chips are compact elements that represent an input, attribute, or action.

## [Container](CONTAINER.md)

A container is the main content of page.

## [Dialog](DIALOG.md)

Dialogs inform users about a task and can contain critical information, required decisions, involve multiple tasks, provide access to destinations in your app and contain a small forms to submit.

## [Divider](DIVIDER.md)

Dividers are thin lines that group content in lists or other containers

## [Expansion](EXPANSION.md)

Expansion contains creation flows and allows lightweight editing of an element.

## [Grid](GRID.md)

Grids are rows and cols system grid. They are most used to organize content.

## [Icon](ICON.md)

Material design system icons are simple, modern, friendly, and sometimes quirky. Each icon is created using our design guidelines to depict in simple and minimal forms the universal concepts used commonly throughout a UI. Ensuring readability and clarity at both large and small sizes, these icons have been optimized for beautiful display on all common platforms and display resolutions.

## [Input](INPUT.md)

Input fields let users enter and edit text.

## [Layout](LAYOUT.md)

Layouts are containers that you can place in any position. There are absolute and fixed elements.

## [List](LIST.md)

Lists are continuous, vertical indexes of text and images. Each item can have up to 3 lines.

## [Main layout](MAIN_LAYOUT.md)

The main layout is a common html structure to setup your pages. For RTL languages set the attribute `dir="rtl"` on `body` element (or any other element).

## [Media](MEDIA.md)

Media can be an image or video element.

## [Menu](MENU.md)

Menus display a list of choices on temporary surfaces.

## [Navigation](NAVIGATION.md)

Navigations are containers that display actions placed horizontally or vertically. Elements, like buttons, chips, images, checkboxes, radios and switches can be placed inside a nav. Some examples are navigation rail or navigation bar.

## [Overlay](OVERLAY.md)

Overlays block user screen and can express an unspecified wait time.

## [Page](PAGE.md)

Pages are containers that can be a main page, multiple pages or just to animate an element.

## [Progress](PROGRESS.md)

Progress displays the length of a process or an unspecified wait time.

## [Radio](RADIO.md)

Radio buttons allow users to select one option from a set.

## [Select](SELECT.md)

Selects display a list of choices on temporary surfaces.

## [Slider](SLIDER.md)

Sliders allow users to make selections from a range of values. There are two types of sliders: continuous and discrete. Default range is 0-100.

## [Snackbar](SNACKBAR.md)

Snackbars provide brief messages about app processes at bottom or top of the screen. It's not recommended to show two or more snackbars at same time.

## [Switch](SWITCH.md)

Switches toggle the state of a single item on or off.

## [Table](TABLE.md)

Tables display sets of data across rows and columns.

## [Tabs](TABS.md)

Tabs organize content across different screens, data sets, and other interactions.

## [Textarea](TEXTAREA.md)

Textarea fields let users enter and edit long text.

## [Tooltip](TOOLTIP.md)

Tooltips displays informative text when users hover over, focus on, or tap an element.

## [Typography](TYPOGRAPHY.md)

Use typography to present your design and content as clearly and efficiently as possible.

## Go to

[Begin](INDEX.md), [Elements](ELEMENTS.md), [Helpers](HELPERS.md), [Settings](SETTINGS.md), [Summary](SUMMARY.md), [Javascript](JAVASCRIPT.md), [beercss.com](https://www.beercss.com)

[Badge](BADGE.md), [Button](BUTTON.md), [Card](CARD.md), [Checkbox](CHECKBOX.md), [Chip](CHIP.md), [Container](CONTAINER.md), [Dialog](DIALOG.md), [Divider](DIVIDER.md), [Expansion](EXPANSION.md), [Grid](GRID.md), [Icon](ICON.md), [Input](INPUT.md), [Layout](LAYOUT.md), [List](LIST.md), [Main layout](MAIN_LAYOUT.md), [Media](MEDIA.md), [Menu](MENU.md), [Navigation](NAVIGATION.md), [Overlay](OVERLAY.md), [Page](PAGE.md), [Progress](PROGRESS.md), [Radio](RADIO.md), [Select](SELECT.md), [Slider](SLIDER.md), [Switch](SWITCH.md), [Table](TABLE.md), [Tabs](TABS.md), [Textarea](TEXTAREA.md), [Snackbar](SNACKBAR.md), [Tooltip](TOOLTIP.md), [Typography](TYPOGRAPHY.md)
