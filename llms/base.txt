Okay, aquí tienes un prompt detallado para un LLM, diseñado para guiar la creación y descripción del layout de un Dashboard de Administración moderno para Faztore, basándose en los requisitos y el contexto de las fuentes proporcionadas, e incorporando tus especificaciones adicionales.

---

**Prompt para LLM: Diseño de Dashboard de Administración para Faztore (MD3 + BeerCSS)**

**Rol:** Actúa como un diseñador y desarrollador experto en UI/UX y frontend, con un profundo conocimiento en sistemas de diseño modernos como Material Design 3 (MD3), frameworks CSS utility-first y preprocesadores, y bibliotecas de componentes como BeerCSS. Estás creando el diseño y layout para la sección de administración de una tienda virtual de tecnología.

**Contexto del Proyecto (Faztore):**
Faztore es una tienda virtual especializada en productos tecnológicos que busca ofrecer una experiencia de compra moderna, ágil y visualmente atractiva, inspirada en patrones de consumo de redes sociales (tipo TikTok/Reels). Prioriza una interfaz minimalista, intuitiva y optimizada para dispositivos móviles. El frontend principal utiliza Astro + SvelteKit/Svelte y TailwindCSS, aunque la documentación para la parte de administración menciona SvelteKit y BeerCSS. El diseño visual general se basa en Material Design 3.

**Objetivo:** Diseñar y describir un layout *sumamente flexible* y *perfectamente responsivo* para el **Dashboard de Administración de Faztore**. Este dashboard debe ser funcional para gestionar productos, categorías, usuarios, pedidos, cupones y contenido del feed.

**Requisitos de Diseño y Estilo:**

1.  **Estilo General:** El diseño debe ser **moderno, limpio y minimalista**, alineado con la estética general de Faztore.
2.  **Sistema de Diseño:** Implementar los principios y directrices de **Material Design 3 (MD3)**. Interpretar MD3 con un enfoque **"expresivo"** para el dashboard, utilizando elementos visuales como **blur, opacidad, border radius, sombras suaves, y micro-animaciones sutiles** en interacciones, manteniendo la coherencia pero con un toque dinámico apropiado para una herramienta de gestión moderna.
3.  **Framework de Estilización:** Utilizar **BeerCSS** como la biblioteca principal para componentes y utilidades de estilo. BeerCSS está inspirado en Material Design. Asegurarse de que los componentes como botones, inputs, tarjetas, tablas, etc., se ajusten al estilo BeerCSS y MD3.
4.  **Flexibilidad:** El layout principal (el área de contenido) debe ser **altamente flexible** para acomodar diferentes tipos de información y interfaces: tablas de datos (productos, pedidos, usuarios), formularios complejos (edición de producto), visualizaciones de métricas (gráficos, tarjetas de resumen), y editores de contenido (para el feed/blog). Debe ser fácil reorganizar o mostrar diferentes "widgets" o secciones de contenido.
5.  **Elementos Estructurales Requeridos:**
    *   **Header:** Barra superior fija o semi-fija. Contendrá elementos clave como el título del dashboard, posiblemente un botón de menú para la sidebar en móviles, notificaciones, y controles de usuario (ej: avatar, logout).
    *   **Sidebar (Barra Lateral):** Elemento de navegación principal. Debe ser **perfectamente responsivo**, colapsando o desapareciendo en pantallas pequeñas y expandiéndose en pantallas más grandes. Contendrá enlaces a las secciones principales de administración (Dashboard, Productos, Pedidos, Usuarios, Cupones, Contenido/Blog, Configuración). Debe tener estados visuales claros (activo, hover). Considerar un modo colapsado con solo íconos para pantallas de tablet o para optimizar espacio.
    *   **Área de Contenido Principal:** La zona donde se mostrarán las diferentes vistas de gestión (tablas de productos, formularios de edición, gráficos de ventas, etc.). Debe ocupar el espacio restante del layout y ser el foco principal.
    *   **Página de Login:** Una vista de inicio de sesión separada, con un diseño limpio y moderno, siguiendo los principios de MD3 y BeerCSS. Debe incluir campos para usuario (ej: email) y contraseña, y un botón de acceso. Considerar opciones de "Recordarme" o enlace a recuperación de contraseña.
6.  **Elementos Visuales Adicionales (Según la Petición del Usuario - **Nota: Estos elementos específicos no están detallados en los documentos fuente y son requisitos nuevos**):
    *   **Fondo:** Incorporar un **modern background grid** como elemento sutil de fondo o en ciertas secciones para añadir profundidad y modernidad, sin distraer del contenido principal.
    *   **Paleta de Colores:** Utilizar una **combinación perfecta de colores verdes y cian** como colores primarios o de acento, aplicados consistentemente según las guías de MD3 para estados interactivos, fondo de elementos clave (sidebar, header, botones), y tipografía. Asegurar que la combinación sea visualmente agradable, accesible y coherente con la identidad tecnológica. Definir cómo se usarán estos colores en modo claro y oscuro. Las fuentes mencionan un sistema de color uniforme MD3 con modo claro/oscuro pero no especifican estos tonos.
7.  **Interactividad y Responsividad:**
    *   Describir cómo la sidebar se adapta a diferentes tamaños de pantalla (ej: colapsa con íconos, se esconde detrás de un botón hamburguesa en móviles).
    *   Explicar cómo el contenido principal se reestructura (ej: tablas se vuelven tarjetas o listas en móviles, formularios se adaptan) para una experiencia de usuario fluida en todos los dispositivos.
    *   Detallar micro-interacciones (hover, click, transiciones) usando los principios de BeerCSS/MD3.
8.  **Optimización:** Mencionar cómo el diseño considera la carga rápida y la usabilidad.

**Instrucciones para la IA:**

Genera una descripción detallada del layout del Dashboard de Administración de Faztore, incluyendo:

1.  Una descripción general de la estructura del layout (Header, Sidebar, Contenido), explicando cómo se adapta a diferentes tamaños de pantalla.
2.  Un desglose del diseño de la Sidebar responsiva.
3.  Una descripción de la Página de Login.
4.  Cómo se aplican los principios de Material Design 3 (incluyendo el enfoque "expresivo") y se integran con el framework BeerCSS en el diseño de los componentes y el layout general.
5.  Cómo se incorporan y utilizan los colores verde y cian (especificados por el usuario) dentro del diseño, respetando las guías de MD3 para accesibilidad y coherencia visual en modos claro/oscuro.
6.  Cómo se implementa el "modern background grid" (especificado por el usuario) sin sobrecargar la interfaz.
7.  Una descripción de cómo el área de contenido principal mantiene su flexibilidad para diferentes tipos de vistas de gestión.
8.  Mencionar brevemente cómo el diseño soporta modo claro y oscuro de manera consistente.

La respuesta debe ser descriptiva y técnica, como si estuvieras especificando el diseño a un equipo de desarrollo frontend. Utiliza términos de UI/UX y describe los elementos visuales y de interacción clave.

---

Este prompt proporciona al LLM toda la información relevante de las fuentes, los requisitos específicos del usuario (colores, grid, flexibilidad extrema), y la estructura deseada para la respuesta, asegurando que el resultado sea relevante, detallado y alineado con el contexto de Faztore.
