import { browser } from '$app/environment';
import { writable } from 'svelte/store';

type Theme = 'light' | 'dark' | 'auto';

class ThemeStore {
	private _theme = writable<Theme>('auto');
	private _systemTheme = writable<'light' | 'dark'>('light');

	constructor() {
		if (browser) {
			this.init();
		}
	}

	private init() {
		// Get saved theme from localStorage
		const saved = localStorage.getItem('theme') as Theme;
		if (saved && ['light', 'dark', 'auto'].includes(saved)) {
			this._currentTheme = saved;
			this._theme.set(saved);
		}

		// Listen for system theme changes
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
		this._currentSystemTheme = mediaQuery.matches ? 'dark' : 'light';
		this._systemTheme.set(this._currentSystemTheme);

		mediaQuery.addEventListener('change', (e) => {
			this._currentSystemTheme = e.matches ? 'dark' : 'light';
			this._systemTheme.set(this._currentSystemTheme);
			this.applyTheme();
		});

		// Subscribe to store changes
		this._theme.subscribe((theme) => {
			this._currentTheme = theme;
		});

		this._systemTheme.subscribe((systemTheme) => {
			this._currentSystemTheme = systemTheme;
		});

		this.applyTheme();
	}

	get theme() {
		return this._theme;
	}

	private _currentTheme: Theme = 'auto';
	private _currentSystemTheme: 'light' | 'dark' = 'light';

	get effectiveTheme(): 'light' | 'dark' {
		return this._currentTheme === 'auto' ? this._currentSystemTheme : this._currentTheme;
	}

	setTheme(theme: Theme) {
		this._currentTheme = theme;
		this._theme.set(theme);
		if (browser) {
			localStorage.setItem('theme', theme);
			this.applyTheme();
		}
	}

	toggle() {
		const current = this.effectiveTheme;
		this.setTheme(current === 'light' ? 'dark' : 'light');
	}

	private applyTheme() {
		if (!browser) return;

		const effective = this.effectiveTheme;

		// Use BeerCSS ui() function for theme management if available
		if (typeof window !== 'undefined' && 'ui' in window && typeof window.ui === 'function') {
			window.ui('mode', effective);
		} else {
			// Fallback to manual class management
			document.body.className = effective;
		}

		// Update meta theme-color for mobile browsers
		const metaThemeColor = document.querySelector('meta[name="theme-color"]');
		if (metaThemeColor) {
			metaThemeColor.setAttribute('content', effective === 'dark' ? '#1f1b16' : '#fffbff');
		}
	}
}

export const themeStore = new ThemeStore();
