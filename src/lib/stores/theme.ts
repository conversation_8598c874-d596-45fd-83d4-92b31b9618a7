import { browser } from '$app/environment';

type Theme = 'light' | 'dark' | 'auto';

class ThemeStore {
	private _currentTheme: Theme = 'auto';
	private _currentSystemTheme: 'light' | 'dark' = 'light';
	private _subscribers: Array<() => void> = [];

	constructor() {
		if (browser) {
			this.init();
		}
	}

	private init() {
		// Get saved theme from localStorage
		const saved = localStorage.getItem('theme') as Theme;
		if (saved && ['light', 'dark', 'auto'].includes(saved)) {
			this._currentTheme = saved;
		}

		// Listen for system theme changes
		const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
		this._currentSystemTheme = mediaQuery.matches ? 'dark' : 'light';

		mediaQuery.addEventListener('change', (e) => {
			this._currentSystemTheme = e.matches ? 'dark' : 'light';
			this.applyTheme();
			this.notifySubscribers();
		});

		// Apply theme immediately
		this.applyTheme();
	}

	get effectiveTheme(): 'light' | 'dark' {
		return this._currentTheme === 'auto' ? this._currentSystemTheme : this._currentTheme;
	}

	get currentTheme(): Theme {
		return this._currentTheme;
	}

	setTheme(theme: Theme) {
		this._currentTheme = theme;
		if (browser) {
			localStorage.setItem('theme', theme);
			this.applyTheme();
		}
		this.notifySubscribers();
	}

	toggle() {
		const current = this.effectiveTheme;
		this.setTheme(current === 'light' ? 'dark' : 'light');
	}

	subscribe(callback: () => void) {
		this._subscribers.push(callback);
		return () => {
			const index = this._subscribers.indexOf(callback);
			if (index > -1) {
				this._subscribers.splice(index, 1);
			}
		};
	}

	private notifySubscribers() {
		this._subscribers.forEach((callback) => callback());
	}

	private applyTheme() {
		if (!browser) return;

		const effective = this.effectiveTheme;

		// Wait for BeerCSS to be available
		const applyBeerCSSTheme = () => {
			if (typeof window !== 'undefined' && 'ui' in window && typeof window.ui === 'function') {
				(window as { ui: (action: string, value?: string) => void }).ui('mode', effective);
			} else {
				// Fallback to manual class management
				document.body.className = effective;
			}
		};

		// Try to apply immediately, or wait for BeerCSS to load
		if (typeof window !== 'undefined' && 'ui' in window) {
			applyBeerCSSTheme();
		} else {
			// Wait for BeerCSS to load
			const checkForBeerCSS = () => {
				if (typeof window !== 'undefined' && 'ui' in window) {
					applyBeerCSSTheme();
				} else {
					setTimeout(checkForBeerCSS, 100);
				}
			};
			checkForBeerCSS();
		}

		// Update meta theme-color for mobile browsers
		const metaThemeColor = document.querySelector('meta[name="theme-color"]');
		if (metaThemeColor) {
			metaThemeColor.setAttribute('content', effective === 'dark' ? '#1f1b16' : '#fffbff');
		}
	}
}

export const themeStore = new ThemeStore();
