/* Faztore Admin - Clean Layout Styles */

/* Ensure proper BeerCSS layout behavior */
body {
	margin: 0;
	padding: 0;
	overflow-x: hidden;
}

/* Fix for left drawer navigation - BeerCSS responsive pattern */
nav.left.drawer {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	width: 280px;
	z-index: 1000;
	overflow-y: auto;
	/* Hide on mobile by default */
	transform: translateX(-100%);
	transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Show drawer on large screens (desktop) */
@media (min-width: 1024px) {
	nav.left.drawer {
		transform: translateX(0);
		position: fixed;
	}
}

/* Show drawer when active (mobile toggle) */
nav.left.drawer.active {
	transform: translateX(0);
}

/* Adjust main content for drawer on large screens */
@media (min-width: 1024px) {
	main.responsive {
		margin-left: 280px;
	}
}

/* Top navigation positioning */
nav.top {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	height: 64px;
	backdrop-filter: blur(8px);
}

/* Adjust top nav for drawer on large screens */
@media (min-width: 1024px) {
	nav.top {
		left: 280px;
	}
}

/* Main content spacing for top nav */
main.responsive {
	padding-top: 64px;
	min-height: calc(100vh - 64px);
	padding-left: 1rem;
	padding-right: 1rem;
}

/* Overlay for mobile drawer */
.drawer-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 999;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.drawer-overlay.active {
	opacity: 1;
	visibility: visible;
}

/* Responsive adjustments */
@media (max-width: 1023px) {
	nav.top {
		left: 0;
	}

	main.responsive {
		margin-left: 0;
	}
}

/* Clean card styling */
.stat-card {
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.stat-card:hover {
	transform: translateY(-2px);
}

/* Action button hover effects */
.action-item {
	transition: all 0.2s ease;
	cursor: pointer;
}

.action-item:hover {
	transform: translateY(-1px);
}

/* Ensure proper spacing in grid */
.grid > div {
	padding: 0.5rem;
}

/* Fix for responsive images */
img.responsive {
	max-width: 100%;
	height: auto;
}

/* Smooth transitions for theme changes */
* {
	transition:
		background-color 0.3s ease,
		color 0.3s ease,
		border-color 0.3s ease;
}

/* Active menu item styling */
nav.left.drawer a.active {
	background-color: var(--primary-container);
	color: var(--on-primary-container);
}

nav.left.drawer a.active i {
	color: var(--on-primary-container);
}

/* Ensure proper spacing in sidebar */
nav.left.drawer a {
	display: flex;
	align-items: center;
	padding: 0.75rem 1rem;
	text-decoration: none;
	color: var(--on-surface);
	border-radius: 0.5rem;
	margin: 0.25rem 0.5rem;
	transition: all 0.2s ease;
}

nav.left.drawer a:hover {
	background-color: var(--surface-container-high);
}

nav.left.drawer a i {
	margin-right: 0.75rem;
	color: var(--on-surface-variant);
}

nav.left.drawer a div {
	font-weight: 500;
}

/* Fix for mobile drawer overlay */
@media (max-width: 1023px) {
	.drawer-overlay.active {
		display: block;
	}

	nav.left.drawer.active + .drawer-overlay {
		opacity: 1;
		visibility: visible;
	}
}

/* Ensure proper content padding */
.page {
	padding: 1rem;
}

@media (min-width: 768px) {
	.page {
		padding: 1.5rem;
	}
}

@media (min-width: 1024px) {
	.page {
		padding: 2rem;
	}
}
