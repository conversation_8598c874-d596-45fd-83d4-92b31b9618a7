/* Faztore Admin - Clean Layout Styles */

/* Ensure proper BeerCSS layout behavior */
body {
	margin: 0;
	padding: 0;
	overflow-x: hidden;
}

/* Fix for left drawer navigation */
nav.left.drawer {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	width: 280px;
	z-index: 1000;
	transform: translateX(-100%);
	transition: transform 0.3s ease;
}

/* Show drawer on medium and large screens */
@media (min-width: 1024px) {
	nav.left.drawer {
		transform: translateX(0);
	}
}

/* Adjust main content for drawer */
@media (min-width: 1024px) {
	main.responsive {
		margin-left: 280px;
	}
}

/* Top navigation positioning */
nav.top {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	height: 64px;
}

@media (min-width: 1024px) {
	nav.top {
		left: 280px;
	}
}

/* Main content spacing for top nav */
main.responsive {
	padding-top: 64px;
	min-height: calc(100vh - 64px);
}

/* Ensure drawer shows when toggled */
nav.left.drawer.active,
nav.left.drawer:target {
	transform: translateX(0);
}

/* Overlay for mobile drawer */
.overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 999;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
}

.overlay.active {
	opacity: 1;
	visibility: visible;
}

/* Responsive adjustments */
@media (max-width: 1023px) {
	nav.top {
		left: 0;
	}

	main.responsive {
		margin-left: 0;
	}
}

/* Clean card styling */
.stat-card {
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.stat-card:hover {
	transform: translateY(-2px);
}

/* Action button hover effects */
.action-item {
	transition: all 0.2s ease;
	cursor: pointer;
}

.action-item:hover {
	transform: translateY(-1px);
}

/* Ensure proper spacing in grid */
.grid > div {
	padding: 0.5rem;
}

/* Fix for responsive images */
img.responsive {
	max-width: 100%;
	height: auto;
}

/* Smooth transitions for theme changes */
* {
	transition:
		background-color 0.3s ease,
		color 0.3s ease,
		border-color 0.3s ease;
}
