<script lang="ts">
	import { page } from '$app/state';
	import { themeStore } from '$lib/stores/theme';

	interface MenuItem {
		href: string;
		icon: string;
		label: string;
		active?: boolean;
	}

	const menuItems: MenuItem[] = [
		{ href: '/', icon: 'dashboard', label: 'Dashboard' },
		{ href: '/categorias', icon: 'category', label: 'Categories' },
		{ href: '/marcas', icon: 'branding_watermark', label: 'Brands' },
		{ href: '/drive', icon: 'folder', label: 'Drive' },
		{ href: '/productos', icon: 'inventory_2', label: 'Products' },
		{ href: '/inventario', icon: 'warehouse', label: 'Inventory' },
		{ href: '/ingresos', icon: 'trending_up', label: 'Income' },
		{ href: '/ventas', icon: 'point_of_sale', label: 'Sales' }
	];

	// Determine active menu item
	const activeItem = $derived(() => {
		const currentPath = page.url.pathname;
		return menuItems.find((item) => {
			if (item.href === '/' && currentPath === '/') return true;
			if (item.href !== '/' && currentPath.startsWith(item.href)) return true;
			return false;
		});
	});

	function toggleTheme() {
		themeStore.toggle();
	}
</script>

<nav class="left drawer surface-container" id="sidebar">
	<!-- Brand/Logo -->
	<header class="padding">
		<a href="/" class="row no-space">
			<i class="extra primary">store</i>
			<div class="padding">
				<h6 class="no-margin">Faztore</h6>
				<small class="secondary-text">Admin Panel</small>
			</div>
		</a>
	</header>

	<!-- Navigation Menu -->
	{#each menuItems as item (item.href)}
		<a
			href={item.href}
			class={activeItem()?.href === item.href ? 'active' : ''}
			data-sveltekit-preload-data="hover"
			data-ui="#sidebar"
		>
			<i>{item.icon}</i>
			<div>{item.label}</div>
		</a>
	{/each}

	<!-- Bottom section -->
	<div class="max"></div>

	<!-- Theme toggle for mobile -->
	<button
		type="button"
		onclick={toggleTheme}
		data-ui="#sidebar"
		class="s m transparent"
		aria-label="Toggle theme"
	>
		<i>{themeStore.effectiveTheme === 'dark' ? 'light_mode' : 'dark_mode'}</i>
		<div>Theme</div>
	</button>

	<a href="/settings" data-ui="#sidebar">
		<i>settings</i>
		<div>Settings</div>
	</a>
	<a href="/help" data-ui="#sidebar">
		<i>help</i>
		<div>Help</div>
	</a>
</nav>
