<script lang="ts">
	import { page } from '$app/state';
	import { themeStore } from '$lib/stores/theme';
	import { onMount } from 'svelte';

	// Reactive theme state
	let currentTheme = $state(themeStore.effectiveTheme);

	// Get page title from data or derive from route
	const pageTitle = $derived(() => {
		if (page.data?.title) return page.data.title;

		const path = page.url.pathname;
		if (path === '/' || path === '/home') return 'Dashboard';
		if (path.includes('categorias')) return 'Categorias';
		if (path.includes('marcas')) return 'Marcas';
		if (path.includes('drive')) return 'Drive';
		if (path.includes('productos')) return 'Productos';
		if (path.includes('inventario')) return 'Inventario';
		if (path.includes('ingresos')) return 'Ingresos';
		if (path.includes('ventas')) return 'Ventas';
		return 'Faztore Admin';
	});

	function toggleTheme() {
		themeStore.toggle();
	}

	// Subscribe to theme changes
	onMount(() => {
		const unsubscribe = themeStore.subscribe(() => {
			currentTheme = themeStore.effectiveTheme;
		});
		return unsubscribe;
	});
</script>

<!-- Top Navigation following BeerCSS pattern -->
<nav class="top surface-container">
	<!-- Mobile menu button - show on small/medium screens -->
	<button class="circle transparent s m" data-ui="#sidebar" aria-label="Toggle menu">
		<i>menu</i>
	</button>

	<!-- Brand/Logo for mobile -->
	<div class="s m">
		<i class="primary">store</i>
	</div>

	<!-- Page title -->
	<h5 class="max no-margin">{pageTitle()}</h5>

	<!-- Theme toggle - visible on small screens, hidden on large -->
	<button class="circle transparent s m" onclick={toggleTheme} aria-label="Toggle theme">
		<i>{currentTheme === 'dark' ? 'light_mode' : 'dark_mode'}</i>
	</button>

	<!-- Actions -->
	<button class="circle transparent" aria-label="Search">
		<i>search</i>
	</button>

	<button class="circle transparent" aria-label="Notifications">
		<i>notifications</i>
	</button>

	<!-- Theme toggle for large screens -->
	<button class="circle transparent l" onclick={toggleTheme} aria-label="Toggle theme">
		<i>{currentTheme === 'dark' ? 'light_mode' : 'dark_mode'}</i>
	</button>

	<button class="circle transparent" aria-label="User menu">
		<i>account_circle</i>
	</button>
</nav>
