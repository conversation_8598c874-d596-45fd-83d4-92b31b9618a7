<script lang="ts">
	let { children } = $props();
	import 'beercss';
	import 'material-dynamic-colors';
	import '../styles.css';
	import { browser } from '$app/environment';
	import { onMount } from 'svelte';
	import { themeStore } from '$lib/stores/theme';

	// Initialize BeerCSS UI system
	onMount(() => {
		if (browser && typeof window !== 'undefined') {
			// Initialize BeerCSS UI system
			const script = document.createElement('script');
			script.src = 'https://cdn.jsdelivr.net/npm/beercss@3.6.13/dist/cdn/beer.min.js';
			script.onload = () => {
				// Set initial theme mode using theme store
				if ('ui' in window && typeof window.ui === 'function') {
					const savedTheme = themeStore.effectiveTheme;
					(window as { ui: (action: string, value?: string) => void }).ui('mode', savedTheme);
				}

				// Setup drawer overlay click handler
				const overlay = document.getElementById('drawer-overlay');
				const sidebar = document.getElementById('sidebar');

				if (overlay && sidebar) {
					overlay.addEventListener('click', () => {
						if (typeof window !== 'undefined' && 'ui' in window) {
							(window as { ui: (selector: string) => void }).ui('#sidebar');
						}
					});
				}
			};
			document.head.appendChild(script);
		}
	});
</script>

{@render children()}
