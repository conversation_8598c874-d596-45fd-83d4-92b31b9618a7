<script lang="ts">
	import { enhance } from '$app/forms';
	import { goto } from '$app/navigation';

	let email = $state('');
	let password = $state('');
	let isLoading = $state(false);
	let showPassword = $state(false);

	function handleSubmit() {
		isLoading = true;
		// Simulate login process
		setTimeout(() => {
			isLoading = false;
			goto('/');
		}, 1500);
	}

	function togglePasswordVisibility() {
		showPassword = !showPassword;
	}
</script>

<svelte:head>
	<title>Login - Faztore Admin</title>
</svelte:head>

<!-- Clean BeerCSS Login Layout -->
<div class="fill middle-align center-align">
	<article
		class="surface-container round large-padding elevate"
		style="max-width: 420px; width: 90%; margin: 1rem;"
	>
		<!-- Header -->
		<div class="center-align large-space">
			<div class="circle extra primary-container">
				<i class="extra primary">store</i>
			</div>
			<div class="space"></div>
			<h4 class="no-margin">Welcome to Faztore</h4>
			<p class="secondary-text">Sign in to your admin account</p>
		</div>

		<!-- Login Form -->
		<form onsubmit={handleSubmit} use:enhance>
			<div class="space"></div>

			<div class="field label prefix border round">
				<input type="email" bind:value={email} required autocomplete="email" id="email" />
				<label for="email">Email Address</label>
				<i>email</i>
			</div>

			<div class="field label suffix border round">
				<input
					type={showPassword ? 'text' : 'password'}
					bind:value={password}
					required
					autocomplete="current-password"
					id="password"
				/>
				<label for="password">Password</label>
				<button
					type="button"
					class="transparent circle small"
					onclick={togglePasswordVisibility}
					aria-label={showPassword ? 'Hide password' : 'Show password'}
				>
					<i>{showPassword ? 'visibility_off' : 'visibility'}</i>
				</button>
			</div>

			<div class="space"></div>

			<nav class="no-space">
				<label class="checkbox">
					<input type="checkbox" />
					<span>Remember me</span>
				</label>
				<div class="max"></div>
				<a href="/auth/forgot-password" class="link">Forgot password?</a>
			</nav>

			<div class="large-space"></div>

			<button type="submit" class="responsive primary round" disabled={isLoading}>
				{#if isLoading}
					<progress class="circle small"></progress>
					<span>Signing in...</span>
				{:else}
					<span>Sign In</span>
				{/if}
			</button>
		</form>

		<!-- Footer -->
		<div class="center-align large-space">
			<p class="secondary-text">
				Don't have an account?
				<a href="/auth/register" class="link">Contact administrator</a>
			</p>
		</div>
	</article>
</div>
