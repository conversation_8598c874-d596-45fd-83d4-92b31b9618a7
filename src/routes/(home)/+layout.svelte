<script lang="ts">
	import type { LayoutProps } from '../$types';
	import { page } from '$app/state';
	import { themeStore } from '$lib/stores/theme';
	import Background from '$lib/components/background.svelte';
	import Navbar from '$lib/components/layout/Navbar.svelte';
	import Sidebar from '$lib/components/layout/Sidebar.svelte';
	import '$lib/styles/layout.css';

	let { children }: LayoutProps = $props();

	// Check if we're on the auth page
	const isAuthPage = $derived(page.url.pathname.startsWith('/auth'));
</script>

<svelte:head>
	<meta name="theme-color" content={themeStore.effectiveTheme === 'dark' ? '#1f1b16' : '#fffbff'} />
</svelte:head>

{#if isAuthPage}
	<!-- Auth layout - full screen with background -->
	<div class="fill middle-align center-align">
		<Background opacity={0.05} maxOpacity={0.15} zIndex="back" />
		{@render children()}
	</div>
{:else}
	<!-- Main admin layout following BeerCSS MAIN_LAYOUT structure -->
	<Background opacity={0.02} maxOpacity={0.05} zIndex="back" />

	<!-- Mobile drawer overlay -->
	<div class="drawer-overlay" id="drawer-overlay"></div>

	<!-- Left Sidebar Navigation (BeerCSS pattern) -->
	<Sidebar />

	<!-- Top Navigation Bar (BeerCSS pattern) -->
	<Navbar />

	<!-- Main Content Area (BeerCSS pattern) -->
	<main class="responsive">
		{@render children()}
	</main>
{/if}
