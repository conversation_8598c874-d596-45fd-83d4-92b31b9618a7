HEY AI, <PERSON><PERSON><PERSON><PERSON> READ THIS FILE

ALWAYS READ LLMS FOLDER BEFORE RESPONDING TO ANY QUESTION

ALWAYS USE SVELTE 5, RUNES, $state, $derived, $effect, $props, etc.

NEVER USE SVELTE 4 SYNTAX: on:click, export let, etc.

INSEAD USE onclick, let { foo = true, bar } = $props(); $effecs, etc.

NEVER USE slot, instead use {@render children()}

NEVER USE hardcoded HTML, instead use Svelte components
never use hardcoded CSS, instead use beercss styles

ALWAYS USE TYPESCRIPT, NEVER USE JAVASCRIPT
AVOID USE ANY TYPE, USE SPECIFIC TYPES INSTEAD

check llms/svelte5.txt for more information
check llms/svelteKit.txt for more information
check llms/beercss.txt for more information

Concistency is key, Concistency UI means

all cards, all buttons, all input all forms all modals, all dialogs, all tables, all lists, all everything must have the same style, the same look, the same feel, the same behavior, the same animations, the same everything.

also, concistency ui means perfect typography, perfect colors, perfect spacing, perfect everything.

all has 1rem margin and padding, all has 1rem font-size, all has 1rem line-height, all has 1rem letter-spacing, all has 1rem border-radius, all has 1rem border-width, all has 1rem border-color, all has 1rem background-color, all has 1rem color, all has 1rem text-transform, all has 1rem text-align, all has 1rem text-decoration, all has 1rem text-shadow

check always material design 3 guidelines for typography, colors, spacing, etc.

